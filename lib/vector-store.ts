interface VectorDocument {
  id: string;
  content: string;
  metadata: {
    title: string;
    source: string;
    type: 'pdf' | 'url';
    topics: string[];
    addedAt: string;
  };
  embedding?: number[];
}

interface SearchResult {
  document: VectorDocument;
  score: number;
}

export class SimpleVectorStore {
  private documents: VectorDocument[] = [];
  private embeddings: Map<string, number[]> = new Map();

  // Extract meaningful topics from content using keyword analysis
  private extractTopicsFromContent(content: string, title: string = ''): string[] {
    const text = `${title} ${content}`.toLowerCase();
    
    // Common business/technical terms that might be relevant
    const topicPatterns = [
      // Business terms
      /\b(policy|policies|procedure|process|workflow|guideline|standard|compliance|regulation)\b/g,
      /\b(company|organization|business|corporate|enterprise|team|department)\b/g,
      /\b(employee|staff|personnel|human resources|hr|management|leadership)\b/g,
      /\b(benefit|benefits|insurance|health|medical|dental|vision|retirement|401k|pto|vacation)\b/g,
      /\b(remote work|telecommuting|flexible|schedule|hybrid|office|workplace)\b/g,
      /\b(training|development|education|learning|skill|certification|professional)\b/g,
      /\b(security|privacy|confidential|data protection|cybersecurity|access control)\b/g,
      /\b(communication|collaboration|meeting|email|slack|teams|zoom)\b/g,
      
      // Technical terms
      /\b(api|rest|graphql|endpoint|service|microservice|architecture)\b/g,
      /\b(database|sql|nosql|mongodb|postgresql|mysql|data|storage)\b/g,
      /\b(authentication|authorization|oauth|jwt|token|login|password)\b/g,
      /\b(cloud|aws|azure|gcp|docker|kubernetes|container|deployment)\b/g,
      /\b(frontend|backend|fullstack|javascript|typescript|react|node|python)\b/g,
      /\b(testing|qa|quality assurance|unit test|integration|automation)\b/g,
      /\b(monitoring|logging|analytics|metrics|performance|optimization)\b/g,
      /\b(version control|git|github|gitlab|repository|branch|merge)\b/g,
      
      // Product terms
      /\b(feature|functionality|requirement|specification|user story|epic)\b/g,
      /\b(ui|ux|interface|design|user experience|usability|accessibility)\b/g,
      /\b(mobile|responsive|web|application|app|platform|system)\b/g,
      /\b(integration|third party|vendor|partner|client|customer)\b/g,
      
      // General business processes
      /\b(onboarding|offboarding|hiring|recruitment|interview|performance review)\b/g,
      /\b(budget|finance|accounting|expense|cost|revenue|pricing)\b/g,
      /\b(project|milestone|deadline|timeline|deliverable|scope)\b/g,
      /\b(support|help desk|ticket|issue|bug|troubleshooting|maintenance)\b/g,
    ];

    const extractedTopics = new Set<string>();
    
    // Extract topics using patterns
    topicPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const topic = match.trim();
          if (topic.length > 2) {
            extractedTopics.add(topic);
          }
        });
      }
    });

    // Also extract important nouns and phrases
    const words = text.split(/\s+/);
    const importantWords = words.filter(word => {
      return word.length > 4 && 
             !['this', 'that', 'with', 'from', 'they', 'have', 'will', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'would', 'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 'them', 'well', 'were'].includes(word.toLowerCase()) &&
             !/^\d+$/.test(word) && // Not just numbers
             /^[a-zA-Z]+$/.test(word); // Only letters
    });

    // Add top frequent important words as topics
    const wordFreq = new Map<string, number>();
    importantWords.forEach(word => {
      const normalized = word.toLowerCase();
      wordFreq.set(normalized, (wordFreq.get(normalized) || 0) + 1);
    });

    // Add words that appear multiple times
    Array.from(wordFreq.entries())
      .filter(([word, count]) => count >= 2)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .forEach(([word]) => extractedTopics.add(word));

    return Array.from(extractedTopics).slice(0, 15); // Limit to 15 topics
  }

  // Enhanced text-to-vector conversion with better word weighting
  private textToVector(text: string): number[] {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
    
    const vector = new Array(200).fill(0); // Increased vector size for better representation
    
    // Create word frequency map
    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
    });
    
    // Convert words to vector with TF weighting
    for (const [word, freq] of wordFreq.entries()) {
      for (let j = 0; j < word.length; j++) {
        const charCode = word.charCodeAt(j);
        const index = (charCode * (j + 1) + word.length) % vector.length;
        vector[index] += freq * Math.log(1 + freq); // TF weighting
      }
    }
    
    // Normalize vector
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    return magnitude > 0 ? vector.map(val => val / magnitude) : vector;
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    const denominator = Math.sqrt(normA) * Math.sqrt(normB);
    return denominator > 0 ? dotProduct / denominator : 0;
  }

  addDocument(document: Omit<VectorDocument, 'embedding'>): void {
    // Remove existing document with same ID
    this.removeDocument(document.id);
    
    // Extract topics from content if not provided or if they seem generic
    let topics = document.metadata.topics;
    if (!topics || topics.length === 0 || this.areTopicsGeneric(topics)) {
      topics = this.extractTopicsFromContent(document.content, document.metadata.title);
    }
    
    const embedding = this.textToVector(document.content + ' ' + topics.join(' '));
    const docWithEmbedding: VectorDocument = {
      ...document,
      metadata: {
        ...document.metadata,
        topics
      },
      embedding
    };
    
    this.documents.push(docWithEmbedding);
    this.embeddings.set(document.id, embedding);
  }

  // Check if topics are too generic
  private areTopicsGeneric(topics: string[]): boolean {
    const genericTopics = ['web', 'development', 'technology', 'api', 'database', 'security', 'devops', 'document', 'management', 'business', 'processes', 'compliance'];
    return topics.every(topic => genericTopics.includes(topic.toLowerCase()));
  }

  removeDocument(id: string): void {
    this.documents = this.documents.filter(doc => doc.id !== id);
    this.embeddings.delete(id);
  }

  search(query: string, limit: number = 5, threshold: number = 0.8): SearchResult[] {
    const queryVector = this.textToVector(query);
    const results: SearchResult[] = [];

    for (const document of this.documents) {
      if (!document.embedding) continue;
      
      const score = this.cosineSimilarity(queryVector, document.embedding);
      
      if (score >= threshold) {
        results.push({ document, score });
      }
    }

    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  // Enhanced search with keyword fallback
  searchWithKeywords(query: string, limit: number = 5, threshold: number = 0.8): SearchResult[] {
    // First try vector search
    const vectorResults = this.search(query, limit, threshold);
    
    if (vectorResults.length > 0) {
      return vectorResults;
    }
    
    // Fallback to keyword search
    const keywords = query.toLowerCase().split(/\s+/).filter(word => word.length > 2);
    const keywordResults: SearchResult[] = [];
    
    for (const document of this.documents) {
      const searchText = `${document.content} ${document.metadata.title} ${document.metadata.topics.join(' ')}`.toLowerCase();
      let score = 0;
      
      for (const keyword of keywords) {
        const regex = new RegExp(keyword, 'gi');
        const matches = searchText.match(regex);
        if (matches) {
          score += matches.length / keywords.length;
        }
      }
      
      if (score > 0) {
        keywordResults.push({ document, score: score / 10 }); // Normalize score
      }
    }
    
    return keywordResults
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  getAllDocuments(): VectorDocument[] {
    return [...this.documents];
  }

  getDocument(id: string): VectorDocument | undefined {
    return this.documents.find(doc => doc.id === id);
  }

  clear(): void {
    this.documents = [];
    this.embeddings.clear();
  }

  // Reprocess all documents to extract better topics
  reprocessTopics(): void {
    for (const document of this.documents) {
      const newTopics = this.extractTopicsFromContent(document.content, document.metadata.title);
      document.metadata.topics = newTopics;
      
      // Regenerate embedding with new topics
      const embedding = this.textToVector(document.content + ' ' + newTopics.join(' '));
      document.embedding = embedding;
      this.embeddings.set(document.id, embedding);
    }
    
    this.save();
  }

  // Get statistics about the knowledge base
  getStats() {
    const totalDocs = this.documents.length;
    const docTypes = this.documents.reduce((acc, doc) => {
      acc[doc.metadata.type] = (acc[doc.metadata.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const allTopics = [...new Set(this.documents.flatMap(doc => doc.metadata.topics))];
    
    return {
      totalDocuments: totalDocs,
      documentTypes: docTypes,
      totalTopics: allTopics.length,
      topics: allTopics,
      sources: this.documents.map(doc => doc.metadata.title)
    };
  }

  // Save to localStorage (in browser environment only)
  save(): void {
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      try {
        const data = {
          documents: this.documents.map(doc => ({
            ...doc,
            embedding: Array.from(doc.embedding || [])
          })),
          timestamp: Date.now()
        };
        localStorage.setItem('vector_store', JSON.stringify(data));
      } catch (error) {
        console.error('Error saving vector store:', error);
      }
    }
  }

  // Load from localStorage (in browser environment only)
  load(): void {
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      try {
        const stored = localStorage.getItem('vector_store');
        if (stored) {
          const data = JSON.parse(stored);
          this.documents = data.documents || [];
          this.embeddings.clear();
          
          for (const doc of this.documents) {
            if (doc.embedding) {
              this.embeddings.set(doc.id, doc.embedding);
            }
          }
        }
      } catch (error) {
        console.error('Error loading vector store:', error);
        this.clear(); // Clear corrupted data
      }
    }
  }

  // Sync with localStorage knowledge base - only add documents that exist in localStorage
  syncWithLocalStorage(): void {
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      try {
        const knowledgeBase = localStorage.getItem('knowledge_base');
        if (knowledgeBase) {
          const items = JSON.parse(knowledgeBase);
          
          // Clear existing documents first to avoid any old sample data
          this.clear();
          
          // Add documents from localStorage
          for (const item of items) {
            if (item.status === 'active') {
              this.addDocument({
                id: item.id,
                content: item.content,
                metadata: {
                  title: item.title,
                  source: item.source,
                  type: item.type,
                  topics: item.topics || [],
                  addedAt: item.addedAt
                }
              });
            }
          }
          
          this.save();
        } else {
          // If no knowledge base exists in localStorage, clear the vector store
          this.clear();
          this.save();
        }
      } catch (error) {
        console.error('Error syncing with localStorage:', error);
        this.clear();
        this.save();
      }
    }
  }
}

// Singleton instance
export const vectorStore = new SimpleVectorStore();