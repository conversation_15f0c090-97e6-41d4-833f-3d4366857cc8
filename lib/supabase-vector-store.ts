import { KnowledgeSourcesDB, DocumentEmbeddingsDB } from './database'
import { GeminiEmbeddingClient } from './gemini-embeddings'
import { KnowledgeItem } from './knowledge-base-types'

interface VectorDocument {
  id: string;
  content: string;
  metadata: {
    title: string;
    source: string;
    sourceUrl?: string;
    type: 'pdf' | 'url';
    topics: string[];
    addedAt: string;
    chunkIndex?: number;
    totalChunks?: number;
    fileSize?: number;
    fileName?: string;
  };
  embedding?: number[];
}

interface SearchResult {
  document: VectorDocument;
  score: number;
  matchType: 'vector' | 'topic' | 'keyword' | 'combined';
}

export class SupabaseVectorStore {
  private geminiClient: GeminiEmbeddingClient | null = null;
  private useGeminiEmbeddings: boolean = false;
  private userId: string | null = null;
  private accessToken: string | null = null;

  constructor(userId?: string, accessToken?: string) {
    this.userId = userId || null;
    this.accessToken = accessToken || null;
    this.initializeGeminiClient();
  }

  private initializeGeminiClient() {
    const geminiApiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || 
                         (typeof window !== 'undefined' ? 
                           localStorage.getItem('gemini_api_key') : null);
    
    if (geminiApiKey) {
      this.geminiClient = new GeminiEmbeddingClient(geminiApiKey);
      this.useGeminiEmbeddings = true;
      console.log('✅ Gemini text-embedding-004 enabled for Supabase');
    } else {
      console.log('⚠️ Gemini API key not found, using simple embeddings');
    }
  }

  // Extract meaningful topics from content
  private extractTopicsFromContent(content: string, title: string = ''): string[] {
    const text = `${title} ${content}`.toLowerCase();
    const extractedTopics = new Set<string>();
    
    // Extract proper nouns and capitalized terms
    const originalText = `${title} ${content}`;
    const properNouns = originalText.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || [];
    properNouns.forEach(noun => {
      if (noun.length > 2 && noun.length < 30) {
        extractedTopics.add(noun.toLowerCase());
      }
    });

    // Extract acronyms
    const acronyms = originalText.match(/\b[A-Z]{2,10}\b/g) || [];
    acronyms.forEach(acronym => {
      if (acronym.length >= 2 && acronym.length <= 6) {
        extractedTopics.add(acronym.toLowerCase());
      }
    });

    // Extract meaningful compound words
    const phrases = text.match(/\b\w+[-_]\w+(?:[-_]\w+)*\b/g) || [];
    phrases.forEach(phrase => {
      if (phrase.length > 4 && phrase.length < 25) {
        extractedTopics.add(phrase.replace(/[-_]/g, ' '));
      }
    });

    // Extract important terms that appear frequently
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);
    const termFrequency = new Map<string, number>();
    
    sentences.forEach(sentence => {
      const words = sentence.split(/\s+/).filter(word => 
        word.length > 3 && 
        word.length < 20 &&
        !/^\d+$/.test(word) &&
        /^[a-zA-Z]+$/.test(word)
      );
      
      words.forEach(word => {
        const normalized = word.toLowerCase();
        termFrequency.set(normalized, (termFrequency.get(normalized) || 0) + 1);
      });
    });

    // Add frequently occurring terms
    const meaningfulTerms = Array.from(termFrequency.entries())
      .filter(([term, frequency]) => frequency >= 2 && term.length > 3)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 15)
      .map(([term]) => term);

    meaningfulTerms.forEach(term => extractedTopics.add(term));

    // Filter out generic terms
    const genericTerms = new Set([
      'the', 'and', 'for', 'are', 'with', 'this', 'that', 'from', 'they', 'have', 
      'will', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'would', 
      'there', 'could', 'other', 'more', 'very', 'what', 'know', 'just', 'first', 
      'into', 'over', 'think', 'also', 'your', 'work', 'life', 'only', 'can', 
      'still', 'should', 'after', 'being', 'now', 'made', 'before', 'here', 
      'through', 'when', 'where', 'much', 'some', 'these', 'many', 'then', 
      'them', 'well', 'were', 'information', 'content', 'text', 'data', 'system',
      'website', 'page', 'section', 'part', 'item', 'list', 'number', 'name'
    ]);

    const finalTopics = Array.from(extractedTopics)
      .filter(topic => 
        !genericTerms.has(topic) && 
        topic.length > 2 && 
        topic.length < 50 &&
        !/^\d+$/.test(topic)
      )
      .slice(0, 20);

    console.log(`📋 Extracted ${finalTopics.length} topics from "${title}":`, finalTopics.slice(0, 10));
    
    return finalTopics;
  }

  // Simple text-to-vector conversion (fallback)
  private textToVector(text: string): number[] {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
    
    const vector = new Array(768).fill(0); // Match Gemini dimensions
    
    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      wordFreq.set(word, (wordFreq.get(word) || 0) + 1);
    });
    
    for (const [word, freq] of wordFreq.entries()) {
      for (let j = 0; j < word.length; j++) {
        const charCode = word.charCodeAt(j);
        const index = (charCode * (j + 1) + word.length) % vector.length;
        vector[index] += freq * Math.log(1 + freq);
      }
    }
    
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    return magnitude > 0 ? vector.map(val => val / magnitude) : vector;
  }

  async addDocument(document: Omit<VectorDocument, 'embedding'>, userId: string): Promise<void> {
    try {
      // Extract topics from content
      const topics = this.extractTopicsFromContent(document.content, document.metadata.title);
      
      // Create knowledge source in database
      const knowledgeItem: Omit<KnowledgeItem, 'id' | 'addedAt'> = {
        title: document.metadata.title,
        type: document.metadata.type,
        source: document.metadata.source,
        sourceUrl: document.metadata.sourceUrl,
        content: document.content,
        topics,
        status: 'active'
      };

      const savedItem = await KnowledgeSourcesDB.create(knowledgeItem, userId, this.accessToken || undefined);
      
      // Generate and store embeddings
      await this.generateAndStoreEmbeddings(savedItem.id, document.content, topics);
      
      console.log(`✅ Added document to Supabase: ${document.metadata.title}`);
    } catch (error) {
      console.error('Error adding document to Supabase:', error);
      throw error;
    }
  }

  async addDocumentWithChunking(document: Omit<VectorDocument, 'embedding'>, userId: string, maxChunkSize: number = 1500): Promise<void> {
    try {
      if (!this.geminiClient || document.content.length <= maxChunkSize) {
        return this.addDocument(document, userId);
      }

      // Chunk the content
      const chunks = this.geminiClient.chunkText(document.content, maxChunkSize, 150);
      
      // Extract topics from full content
      const topics = this.extractTopicsFromContent(document.content, document.metadata.title);
      
      // Create knowledge source with the provided user ID
      const knowledgeItem: Omit<KnowledgeItem, 'id' | 'addedAt'> = {
        title: document.metadata.title,
        type: document.metadata.type,
        source: document.metadata.source,
        sourceUrl: document.metadata.sourceUrl,
        content: document.content,
        topics,
        status: 'active',
        createdBy: userId, // This will be mapped to created_by in the database layer
        fileSize: document.metadata.fileSize,
        fileName: document.metadata.fileName
      };

      const savedItem = await KnowledgeSourcesDB.create(knowledgeItem, userId, this.accessToken || undefined);
      
      // Generate embeddings for each chunk
      for (let i = 0; i < chunks.length; i++) {
        await this.generateAndStoreEmbeddings(savedItem.id, chunks[i], topics, i);
      }
      
      console.log(`✅ Added chunked document to Supabase: ${document.metadata.title} (${chunks.length} chunks)`);
    } catch (error) {
      console.error('Error adding chunked document to Supabase:', error);
      throw error;
    }
  }

  private async generateAndStoreEmbeddings(sourceId: string, content: string, topics: string[], chunkIndex: number = 0): Promise<void> {
    try {
      let embedding: number[];
      
      if (this.useGeminiEmbeddings && this.geminiClient) {
        const textForEmbedding = `${content}\n\nTopics: ${topics.join(', ')}`;
        embedding = await this.geminiClient.generateEmbedding(textForEmbedding);
      } else {
        embedding = this.textToVector(content + ' ' + topics.join(' '));
      }
      
      await DocumentEmbeddingsDB.create(sourceId, content, embedding, chunkIndex);
    } catch (error) {
      console.error('Error generating and storing embeddings:', error);
      // Store with simple embedding as fallback
      const embedding = this.textToVector(content + ' ' + topics.join(' '));
      await DocumentEmbeddingsDB.create(sourceId, content, embedding, chunkIndex);
    }
  }

  async removeDocument(id: string, userId: string): Promise<void> {
    try {
      // Delete embeddings first
      await DocumentEmbeddingsDB.deleteBySourceId(id);
      
      // Delete knowledge source
      await KnowledgeSourcesDB.delete(id, userId);
      
      console.log(`✅ Removed document from Supabase: ${id}`);
    } catch (error) {
      console.error('Error removing document from Supabase:', error);
      throw error;
    }
  }

  // Helper function to validate UUIDs
  private isValidUUID(uuid: string | undefined): boolean {
    return typeof uuid === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid);
  }

  async search(query: string, limit: number = 5, threshold: number = 0.1): Promise<SearchResult[]> {
    try {
      // Generate query embedding
      let queryVector: number[];
      if (this.useGeminiEmbeddings && this.geminiClient) {
        queryVector = await this.geminiClient.generateQueryEmbedding(query);
      } else {
        queryVector = this.textToVector(query);
      }
      
      // Search similar embeddings
      const similarEmbeddings = await DocumentEmbeddingsDB.searchSimilar(queryVector, limit * 2, threshold);
      
      // Log and filter out embeddings with invalid sourceIds
      const embeddingsWithInvalidIds = similarEmbeddings.filter(e => !this.isValidUUID(e.sourceId));
      if (embeddingsWithInvalidIds.length > 0) {
        console.warn('⚠️ Found embeddings with invalid sourceIds:', {
          count: embeddingsWithInvalidIds.length,
          sample: embeddingsWithInvalidIds.slice(0, 3).map(e => ({
            id: e.id,
            sourceId: e.sourceId,
            contentPreview: e.content?.substring(0, 50) + '...',
            similarity: e.similarity
          }))
        });
      }

      // Filter out embeddings with invalid sourceIds and get unique sourceIds
      const validEmbeddings = similarEmbeddings.filter(e => this.isValidUUID(e.sourceId));
      const sourceIds = [...new Set(validEmbeddings.map(e => e.sourceId))];
      
      if (sourceIds.length === 0) {
        console.log('ℹ️ No valid source IDs found in search results');
        return [];
      }

      console.log(`🔍 Fetching ${sourceIds.length} knowledge sources`);
      const sources = await Promise.all(
        sourceIds.map(async id => {
          try {
            return await KnowledgeSourcesDB.getById(id);
          } catch (error) {
            console.error(`❌ Failed to fetch knowledge source ${id}:`, error);
            return null;
          }
        })
      );
      
      const validSources = sources.filter((s): s is KnowledgeItem => s !== null);
      const sourceMap = new Map(validSources.map(s => [s.id, s]));
      
      if (validSources.length < sources.length) {
        console.warn(`⚠️ Failed to fetch ${sources.length - validSources.length} knowledge sources`);
      }
      
      // Build search results
      const results: SearchResult[] = similarEmbeddings
        .map(embedding => {
          const source = sourceMap.get(embedding.sourceId);
          if (!source) return null;
          
          return {
            document: {
              id: source.id,
              content: embedding.content,
              metadata: {
                title: source.title,
                source: source.source,
                sourceUrl: source.sourceUrl,
                type: source.type,
                topics: source.topics,
                addedAt: source.addedAt.toISOString(),
                chunkIndex: embedding.chunkIndex
              }
            },
            score: embedding.similarity,
            matchType: 'vector' as const
          };
        })
        .filter(Boolean) as SearchResult[];
      
      return results.slice(0, limit);
    } catch (error) {
      console.error('Error searching Supabase vector store:', error);
      return [];
    }
  }

  async getAllDocuments(userId?: string): Promise<VectorDocument[]> {
    try {
      // If no userId provided and no instance userId, get all documents (public access)
      const targetUserId = userId ?? this.userId;
      const sources = await KnowledgeSourcesDB.getAll(targetUserId ?? undefined);

      return sources.map(source => ({
        id: source.id,
        content: source.content,
        metadata: {
          title: source.title,
          source: source.source,
          sourceUrl: source.sourceUrl,
          type: source.type,
          topics: source.topics,
          addedAt: source.addedAt.toISOString()
        }
      }));
    } catch (error) {
      console.error('Error getting all documents from Supabase:', error);
      return [];
    }
  }

  async getStats(userId?: string) {
    try {
      // If no userId provided and no instance userId, get stats for all documents (public access)
      const targetUserId = userId ?? this.userId;
      const documents = await this.getAllDocuments(targetUserId ?? undefined);
      const docTypes = documents.reduce((acc, doc) => {
        acc[doc.metadata.type] = (acc[doc.metadata.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const allTopics = [...new Set(documents.flatMap(doc => doc.metadata.topics))];

      return {
        totalDocuments: documents.length,
        documentTypes: docTypes,
        totalTopics: allTopics.length,
        topics: allTopics,
        sources: documents.map(doc => doc.metadata.title),
        embeddingType: this.useGeminiEmbeddings ? 'Gemini' : 'Simple'
      };
    } catch (error) {
      console.error('Error getting stats from Supabase:', error);
      return {
        totalDocuments: 0,
        documentTypes: {},
        totalTopics: 0,
        topics: [],
        sources: [],
        embeddingType: this.useGeminiEmbeddings ? 'Gemini' : 'Simple'
      };
    }
  }

  // Enhanced search with multiple strategies
  async searchWithKeywords(query: string, limit: number = 5, threshold: number = 0.8, userId?: string): Promise<SearchResult[]> {
    console.log(`🔍 Starting Supabase enhanced search for: "${query}"`);

    try {
      // Get all documents for keyword/topic search
      // If no userId provided and no instance userId, search all documents (public access)
      const targetUserId = userId ?? this.userId;
      const allDocuments = await this.getAllDocuments(targetUserId ?? undefined);
      console.log(`📊 Available documents: ${allDocuments.length}`);

      if (allDocuments.length === 0) {
        return [];
      }

      // Vector search
      const vectorResults = await this.search(query, limit * 2, threshold);
      console.log(`🧠 Vector search found: ${vectorResults.length} results`);

      // Topic-based search
      const topicResults = this.topicBasedSearch(query, allDocuments);
      console.log(`📋 Topic search found: ${topicResults.length} results`);

      // Keyword search
      const keywordResults = this.keywordSearch(query, allDocuments);
      console.log(`🔤 Keyword search found: ${keywordResults.length} results`);
      
      // Combine and deduplicate results
      const combinedResults = new Map<string, SearchResult>();
      
      // Add topic results with highest weight
      topicResults.forEach(result => {
        combinedResults.set(result.document.id, {
          ...result,
          score: result.score * 1.3,
          matchType: 'topic'
        });
      });
      
      // Add vector results
      vectorResults.forEach(result => {
        const existing = combinedResults.get(result.document.id);
        if (existing) {
          existing.score = Math.max(existing.score, result.score) + 0.15;
          existing.matchType = 'combined';
        } else {
          combinedResults.set(result.document.id, result);
        }
      });
      
      // Add keyword results
      keywordResults.forEach(result => {
        const existing = combinedResults.get(result.document.id);
        if (existing) {
          existing.score = Math.max(existing.score, result.score) + 0.1;
          existing.matchType = 'combined';
        } else {
          combinedResults.set(result.document.id, result);
        }
      });
      
      const finalResults = Array.from(combinedResults.values())
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);
      
      console.log(`✅ Final Supabase search results: ${finalResults.length}`);
      finalResults.forEach((result, i) => {
        console.log(`  ${i + 1}. ${result.document.metadata.title} (${result.matchType}, score: ${result.score.toFixed(3)})`);
      });
      
      return finalResults;
    } catch (error) {
      console.error('Error in Supabase enhanced search:', error);
      return [];
    }
  }

  private topicBasedSearch(query: string, documents: VectorDocument[]): SearchResult[] {
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(/\s+/).filter(word => word.length > 2);
    const results: SearchResult[] = [];

    for (const document of documents) {
      let topicScore = 0;
      
      for (const topic of document.metadata.topics) {
        const topicLower = topic.toLowerCase();
        
        if (queryWords.some(word => topicLower.includes(word) || word.includes(topicLower))) {
          topicScore += 3;
        }
        
        for (const word of queryWords) {
          if (word.length > 3 && topicLower.includes(word.substring(0, Math.min(word.length, 6)))) {
            topicScore += 1;
          }
        }
      }

      // Check title for topic relevance
      const titleLower = document.metadata.title.toLowerCase();
      for (const word of queryWords) {
        if (titleLower.includes(word)) {
          topicScore += 2;
        }
      }

      if (topicScore > 0) {
        results.push({
          document,
          score: Math.min(topicScore / 8, 1),
          matchType: 'topic'
        });
      }
    }

    return results.sort((a, b) => b.score - a.score);
  }

  private keywordSearch(query: string, documents: VectorDocument[]): SearchResult[] {
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(/\s+/).filter(word => word.length > 2);
    const results: SearchResult[] = [];
    
    for (const document of documents) {
      const searchText = `${document.content} ${document.metadata.title} ${document.metadata.topics.join(' ')}`.toLowerCase();
      let score = 0;
      
      // Check for exact phrase matches
      if (searchText.includes(queryLower)) {
        score += 5;
      }
      
      // Individual word matching
      for (const word of queryWords) {
        const exactMatches = (searchText.match(new RegExp(`\\b${word}\\b`, 'g')) || []).length;
        score += exactMatches * 1;
      }
      
      if (score > 0) {
        results.push({
          document,
          score: score / 10,
          matchType: 'keyword'
        });
      }
    }
    
    return results.sort((a, b) => b.score - a.score);
  }
}